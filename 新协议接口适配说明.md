# 新协议接口适配说明

## 概述

根据`新协议接口.txt`文档，对bfv-bot项目进行了新协议接口的适配工作。主要涉及以下几个新接口的实现和现有接口的更新。

## 新增接口实现

### 1. 获取群列表 (`/get_group_list`)

**新增文件/函数：**
- `src/model/dto/bot.go` - 添加了 `GetGroupListResp` 和 `GetGroupListData` 结构体
- `src/bot/group/manage.go` - 添加了 `GetGroupList()` 函数
- `src/cmd/private.go` - 添加了 `opGroupList()` 管理命令

**使用方式：**
```
私聊机器人发送: op=grouplist
```

### 2. 获取消息 (`/get_msg`)

**新增文件/函数：**
- `src/model/dto/bot.go` - 添加了 `GetMsgResp`、`GetMsgData`、`MsgSender` 结构体
- `src/bot/group/manage.go` - 添加了 `GetMsg()` 函数
- `src/cmd/private.go` - 添加了 `opGetMsg()` 管理命令

**使用方式：**
```
私聊机器人发送: op=getmsg=<消息ID>
```

### 3. 获取群历史聊天记录 (`/get_group_msg_history`)

**新增文件/函数：**
- `src/model/dto/bot.go` - 添加了 `GetGroupMsgHistoryResp`、`GetGroupMsgHistoryData`、`GroupHistoryMessage` 结构体
- `src/bot/group/manage.go` - 添加了 `GetGroupMsgHistory()` 函数
- `src/cmd/private.go` - 添加了 `opGroupHistory()` 管理命令

**使用方式：**
```
私聊机器人发送: op=grouphistory=<群号,消息ID[,数量]>
例如: op=grouphistory=123456789,1001,10
```

### 4. 处理加群请求/邀请 (`/set_group_add_request`)

**新增/更新文件/函数：**
- `src/model/dto/bot.go` - 添加了 `SetGroupAddRequestResp` 结构体
- `src/model/common/req/common.go` - 在 `AddGroupData` 结构体中添加了 `Flag` 字段
- `src/bot/group/manage.go` - 添加了 `SetGroupAddRequest()` 函数
- `src/api/event.go` - 更新了加群请求处理逻辑，使用新的API接口

## 更新的现有接口

### 1. 设置群名片 (`/set_group_card`)
- 现有实现已符合新协议要求，无需修改

### 2. 设置群全体禁言 (`/set_group_whole_ban`)
- 现有实现已符合新协议要求，无需修改

### 3. 撤回消息 (`/delete_msg`)
- 现有实现已符合新协议要求，无需修改

## 主要变更点

### 1. 数据模型更新
- 扩展了 `src/model/dto/bot.go` 文件，添加了所有新接口所需的数据结构
- 更新了 `src/model/common/req/common.go` 中的 `AddGroupData` 结构体

### 2. API函数实现
- 在 `src/bot/group/manage.go` 中添加了4个新的API调用函数
- 所有新函数都遵循现有的错误处理模式和日志记录方式

### 3. 管理命令扩展
- 在 `src/cmd/private.go` 中添加了3个新的管理命令
- 更新了帮助信息，包含新命令的使用说明

### 4. 事件处理逻辑更新
- 更新了 `src/api/event.go` 中的加群请求处理逻辑
- 从旧的响应方式改为使用新的 `SetGroupAddRequest` API

## 新增管理命令

管理员可以通过私聊机器人使用以下新命令：

1. **获取群列表**: `op=grouplist`
2. **获取消息详情**: `op=getmsg=<消息ID>`
3. **获取群历史记录**: `op=grouphistory=<群号,消息ID[,数量]>`

## 兼容性说明

- 所有现有功能保持不变
- 新增功能不会影响现有的机器人操作
- 加群请求处理逻辑已更新为使用新协议，但处理流程保持一致

## 注意事项

1. 确保QQ机器人后端（如NapCat）支持这些新的API接口
2. 新的加群请求处理需要后端提供 `flag` 字段
3. 所有新功能都需要管理员权限才能使用

## 测试建议

1. 测试新的管理命令是否正常工作
2. 验证加群请求处理是否使用新的API接口
3. 确认现有功能没有受到影响
